from functools import cache

from langchain.chat_models import init_chat_model
from langchain_core.language_models import BaseChatModel
from openai import OpenAI

from config.globalconfig import get_or_create_settings_ins

@cache
def get_chat_model(model_name: str = "default") -> BaseChatModel:
    config = get_or_create_settings_ins()
    models = config.models
    model_cfg = getattr(models, "default")
    if model_name in models.model_fields_set:
        model_cfg = getattr(models, model_name)
    llm = config.llms[model_cfg]
    return init_chat_model(
        llm.model,
        model_provider=llm.provider,
        **(llm.external_args or {}),
    )

@cache
def get_coordinate_model() -> OpenAI:
    """获取坐标模型客户端"""
    config = get_or_create_settings_ins()
    models = config.models
    coordinate_model_name = getattr(models, "coordinate")
    llm = config.llms[coordinate_model_name]

    # 从配置中获取base_url和api_key
    external_args = llm.external_args or {}
    base_url = external_args.get("base_url")
    api_key = external_args.get("api_key")

    return OpenAI(
        base_url=base_url,
        api_key=api_key
    )

@cache
def get_coordinate_model_name() -> str:
    """获取坐标模型名称"""
    config = get_or_create_settings_ins()
    models = config.models
    coordinate_model_name = getattr(models, "coordinate")
    llm = config.llms[coordinate_model_name]
    return llm.model

@cache
def get_vision_model() -> OpenAI:
    """获取视觉模型客户端"""
    config = get_or_create_settings_ins()
    models = config.models
    vision_model_name = getattr(models, "vision")
    llm = config.llms[vision_model_name]

    # 从配置中获取base_url和api_key
    external_args = llm.external_args or {}
    base_url = external_args.get("base_url")
    api_key = external_args.get("api_key")

    return OpenAI(
        base_url=base_url,
        api_key=api_key
    )

@cache
def get_vision_model_name() -> str:
    """获取视觉模型名称"""
    config = get_or_create_settings_ins()
    models = config.models
    vision_model_name = getattr(models, "vision")
    llm = config.llms[vision_model_name]
    return llm.model
